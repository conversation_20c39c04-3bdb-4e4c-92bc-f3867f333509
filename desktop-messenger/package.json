{"name": "social-network-messenger", "version": "1.0.0", "description": "Cross-platform desktop messenger for Social Network", "main": "main/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build:win": "electron-builder --win", "build:mac": "electron-builder --mac", "build:linux": "electron-builder --linux", "dist": "npm run build", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["electron", "messenger", "chat", "social-network", "cross-platform"], "author": "Social Network Team", "license": "MIT", "devDependencies": {"electron": "^32.0.0", "electron-builder": "^25.0.0"}, "dependencies": {"electron-store": "^10.0.0"}, "build": {"appId": "com.socialnetwork.messenger", "productName": "Social Network Messenger", "directories": {"output": "dist"}, "files": ["main/**/*", "preload/**/*", "renderer/**/*", "assets/**/*", "package.json"], "mac": {"category": "public.app-category.social-networking", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}]}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}]}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "category": "Network"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}